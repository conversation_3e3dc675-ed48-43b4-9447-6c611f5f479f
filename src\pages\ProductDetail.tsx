import { useParams, Navigate, Link } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { getProductBySlug, getRelatedProducts } from '@/utils/productUtils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ProductImageGallery, ProductFeatures, ProductSpecifications, ProductCard } from '@/components/product';
import { Download, ArrowRight, CheckCircle, Phone, Mail } from 'lucide-react';

const ProductDetail = () => {
  const { slug } = useParams<{ slug: string }>();

  if (!slug) {
    return <Navigate to="/products" replace />;
  }

  const product = getProductBySlug(slug);

  if (!product) {
    return <Navigate to="/products" replace />;
  }

  const relatedProducts = getRelatedProducts(product, 3);
  const mainImage = product.images.find(img => img.isMain) || product.images[0];

  return (
    <div className="min-h-screen">
      <Navigation />
      <div className="pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <ol className="flex items-center space-x-2 text-sm text-muted-foreground">
              <li><Link to="/" className="hover:text-primary transition-colors">Home</Link></li>
              <li>/</li>
              <li><Link to="/products" className="hover:text-primary transition-colors">Products</Link></li>
              <li>/</li>
              <li className="text-foreground">{product.title}</li>
            </ol>
          </nav>

          {/* Product Header */}
          <div className="mb-12">
            <div className="flex flex-wrap items-center gap-2 mb-4">
              <Badge variant="secondary" className="text-xs">
                {product.category.replace('-', ' ').toUpperCase()}
              </Badge>
              {product.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-primary bg-clip-text text-transparent">
              {product.title}
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl">
              {product.shortDescription}
            </p>
          </div>

          {/* Product Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            {/* Product Images */}
            <ProductImageGallery images={product.images} />

            {/* Product Info */}
            <div className="space-y-8">
              {/* Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-primary">Product Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {product.fullDescription}
                  </p>
                </CardContent>
              </Card>

              {/* Key Features */}
              <ProductFeatures
                features={product.features}
                title="Key Features"
                layout="list"
                showIcons={true}
              />

              {/* Quick Actions */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="flex-1 bg-gradient-primary hover:opacity-90">
                  <Phone className="mr-2 h-4 w-4" />
                  Request Quote
                </Button>
                <Button variant="outline" className="flex-1">
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Sales
                </Button>
              </div>
            </div>
          </div>

          {/* Detailed Information Tabs */}
          <div className="mb-16">
            <Tabs defaultValue="specifications" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="specifications">Specifications</TabsTrigger>
                <TabsTrigger value="applications">Applications</TabsTrigger>
                <TabsTrigger value="downloads">Downloads</TabsTrigger>
              </TabsList>

              <TabsContent value="specifications" className="mt-8">
                <ProductSpecifications
                  specifications={product.specifications}
                  title="Technical Specifications"
                  description={`Detailed technical specifications for ${product.title}`}
                  columns={2}
                />
              </TabsContent>

              <TabsContent value="applications" className="mt-8">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {product.applications.map((application, index) => (
                    <Card key={index}>
                      <CardHeader>
                        <CardTitle className="text-primary">{application.sector}</CardTitle>
                        <CardDescription>{application.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {application.benefits.map((benefit, idx) => (
                            <li key={idx} className="text-sm flex items-center">
                              <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                              {benefit}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="downloads" className="mt-8">
                {product.downloads.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {product.downloads.map((download, index) => (
                      <Card key={index} className="hover:shadow-lg transition-shadow">
                        <CardHeader>
                          <CardTitle className="text-lg">{download.title}</CardTitle>
                          <CardDescription>
                            {download.format} • {download.fileSize}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <Button className="w-full" variant="outline">
                            <Download className="mr-2 h-4 w-4" />
                            Download {download.format}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="text-center py-8">
                      <p className="text-muted-foreground">
                        Downloads will be available soon. Contact our sales team for more information.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* Related Products */}
          {relatedProducts.length > 0 && (
            <div className="mb-16">
              <h2 className="text-3xl font-bold mb-8 text-center bg-gradient-primary bg-clip-text text-transparent">
                Related Products
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {relatedProducts.map((relatedProduct) => (
                  <ProductCard
                    key={relatedProduct.id}
                    product={relatedProduct}
                    showCategory={false}
                    showTags={false}
                    maxFeatures={2}
                  />
                ))}
              </div>
            </div>
          )}

          <Separator className="mb-16" />

          {/* CTA Section */}
          <Card className="bg-gradient-to-r from-primary/5 to-accent/5 border-primary/20">
            <CardContent className="text-center py-12">
              <h2 className="text-3xl font-bold mb-4 bg-gradient-primary bg-clip-text text-transparent">
                Ready to Get Started?
              </h2>
              <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
                Contact our technical experts for detailed specifications, custom configurations,
                and competitive pricing for {product.title}.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <Button className="flex-1 bg-gradient-primary hover:opacity-90 shadow-primary">
                  <Phone className="mr-2 h-4 w-4" />
                  Request Quote
                </Button>
                <Button variant="outline" className="flex-1 border-primary text-primary hover:bg-primary hover:text-white">
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Sales
                </Button>
              </div>
              <div className="mt-8 pt-8 border-t border-border/50">
                <p className="text-sm text-muted-foreground">
                  Need technical support? Call us at <span className="font-semibold text-primary">+****************</span> or
                  email <span className="font-semibold text-primary"><EMAIL></span>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ProductDetail;
